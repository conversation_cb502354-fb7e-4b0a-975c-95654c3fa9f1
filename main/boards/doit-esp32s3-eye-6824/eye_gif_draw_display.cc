#include "eye_gif_draw_display.h"
#include "gif_config.h"
#include "config.h"
#include <esp_log.h>
#include <esp_err.h>
#include <algorithm>
#include <cstring>
#include <string>
#include <esp_heap_caps.h>
#include "font_awesome_symbols.h"
#include "eye_gif_resources.h"
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <freertos/task.h>
#include <esp_timer.h>
#include "AnimatedGIF.h"  // Now from components/AnimateGIF_esp

#define TAG "EyeGifDrawDisplay"

// AnimatedGIF 回调函数的上下文结构

struct AnimatedGIFContext {
    EyeGifDrawDisplay* display;
    uint8_t screen_id;
    uint16_t* current_frame_buffer;
    uint32_t frame_width;
    uint32_t frame_height;
};

// 全局C风格回调函数
extern "C" void GlobalAnimatedGIFDrawCallback(GIFDRAW* pDraw) {
    if (!pDraw->pUser) {
        ESP_LOGE("GlobalGIFCallback", "❌ pUser is NULL!");
        return;
    }

    // 直接实现绘制逻辑
    AnimatedGIFContext* ctx = (AnimatedGIFContext*)pDraw->pUser;
    if (!ctx || !ctx->current_frame_buffer) {
        return;
    }

    // 计算当前行在帧缓冲区中的位置
    uint16_t* line_buffer = &ctx->current_frame_buffer[pDraw->y * ctx->frame_width];

    // 将像素数据转换为RGB565格式
    if (pDraw->pPalette && pDraw->iWidth <= (int)ctx->frame_width) {
        int pixels_written = 0;
        for (int x = 0; x < pDraw->iWidth; x++) {
            uint8_t pixel_index = pDraw->pPixels[x];

            // 处理透明像素
            if (pDraw->ucHasTransparency && pixel_index == pDraw->ucTransparent) {
                // 透明像素保持原值或设为背景色
                continue;
            }

            // 从调色板获取RGB565颜色
            uint16_t color = pDraw->pPalette[pixel_index];
            line_buffer[x] = color;
            pixels_written++;

        }

    } else {
        ESP_LOGE("GlobalGIFCallback", "❌ No palette or invalid width: palette=%p, width=%d, frame_width=%ld",
                 pDraw->pPalette, pDraw->iWidth, ctx->frame_width);
    }
}

// AnimatedGIF 内存分配回调
void* EyeGifDrawDisplay::AnimatedGIFAlloc(uint32_t size) {
    // 强制所有AnimatedGIF分配都使用PSRAM，避免消耗内部RAM
    void* ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (!ptr) {
        ESP_LOGE(TAG, "❌ Failed to allocate %lu bytes in PSRAM for AnimatedGIF", size);
        return nullptr;
    }
    ESP_LOGI(TAG, "✅ AnimatedGIF alloc: %lu bytes in PSRAM -> %p", size, ptr);
    return ptr;
}

// AnimatedGIF 内存释放回调
void EyeGifDrawDisplay::AnimatedGIFFree(void* buffer) {
    if (buffer) {
        ESP_LOGD(TAG, "AnimatedGIF free: %p", buffer);
        heap_caps_free(buffer);
    }
}

// AnimatedGIF 绘制回调函数 (必须是静态函数，因为C库需要C风格的函数指针)
void EyeGifDrawDisplay::AnimatedGIFDrawCallback(GIFDRAW* pDraw) {
    static int callback_count = 0;
    callback_count++;

    if (callback_count <= 5) { // 只打印前5次调用
        ESP_LOGI("AnimatedGIF", "🎨 Callback #%d: y=%d, width=%d, has_palette=%d, has_transparency=%d",
                 callback_count, pDraw->y, pDraw->iWidth, pDraw->pPalette ? 1 : 0, pDraw->ucHasTransparency);
    }

    AnimatedGIFContext* ctx = (AnimatedGIFContext*)pDraw->pUser;
    if (!ctx || !ctx->current_frame_buffer) {
        ESP_LOGE("AnimatedGIF", "❌ Invalid context: ctx=%p, frame_buffer=%p",
                 ctx, ctx ? ctx->current_frame_buffer : nullptr);
        return;
    }

    // 计算当前行在帧缓冲区中的位置
    uint16_t* line_buffer = &ctx->current_frame_buffer[pDraw->y * ctx->frame_width];

    // 将像素数据转换为RGB565格式
    if (pDraw->pPalette && pDraw->iWidth <= (int)ctx->frame_width) {
        int pixels_written = 0;
        for (int x = 0; x < pDraw->iWidth; x++) {
            uint8_t pixel_index = pDraw->pPixels[x];

            // 处理透明像素
            if (pDraw->ucHasTransparency && pixel_index == pDraw->ucTransparent) {
                // 透明像素保持原值或设为背景色
                continue;
            }

            // 从调色板获取RGB565颜色
            uint16_t color = pDraw->pPalette[pixel_index];
            line_buffer[x] = color;
            pixels_written++;

            // 调试：打印前几个像素的颜色
            if (callback_count <= 2 && pDraw->y == 0 && x < 4) {
                ESP_LOGI("AnimatedGIF", "  Pixel[%d,%d]: index=%d -> color=0x%04x", x, pDraw->y, pixel_index, color);
            }
        }

        if (callback_count <= 5) {
            ESP_LOGI("AnimatedGIF", "  Line %d: wrote %d pixels", pDraw->y, pixels_written);
        }
    } else {
        ESP_LOGE("AnimatedGIF", "❌ No palette or invalid width: palette=%p, width=%d, frame_width=%ld",
                 pDraw->pPalette, pDraw->iWidth, ctx->frame_width);
    }
}

// 内存使用情况检查函数
static void log_memory_usage() {
    size_t free_heap = heap_caps_get_free_size(MALLOC_CAP_DEFAULT);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t min_free_internal = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
    size_t min_free_psram = heap_caps_get_minimum_free_size(MALLOC_CAP_SPIRAM);

    ESP_LOGI(TAG, "Memory usage:");
    ESP_LOGI(TAG, "  Internal RAM - Free: %ld bytes, Min free: %ld bytes", (long)free_internal, (long)min_free_internal);
    ESP_LOGI(TAG, "  PSRAM - Free: %ld bytes, Min free: %ld bytes", (long)free_psram, (long)min_free_psram);
    ESP_LOGI(TAG, "  Total heap - Free: %ld bytes", (long)free_heap);

    // 计算帧缓冲区使用的内存
    uint32_t frame_buffer_size = DISPLAY_WIDTH * DISPLAY_HEIGHT * 2; // RGB565 = 2字节/像素
    uint32_t line_buffer_size = DISPLAY_WIDTH * 8 * 2; // 行缓冲区 (8行)
    uint32_t cache_buffer_size = frame_buffer_size * 10; // 10帧缓存

    ESP_LOGI(TAG, "  Frame buffer config: %dx%d = %lu pixels per buffer",
             DISPLAY_WIDTH, DISPLAY_HEIGHT, (uint32_t)(DISPLAY_WIDTH * DISPLAY_HEIGHT));
    ESP_LOGI(TAG, "  PSRAM usage per screen: Frame=%lu + Cache=%lu = %lu bytes",
             frame_buffer_size, cache_buffer_size, frame_buffer_size + cache_buffer_size);
    ESP_LOGI(TAG, "  Internal RAM usage per screen: Line buffer=%lu bytes (8 lines)",
             line_buffer_size);
    ESP_LOGI(TAG, "  Total dual screen: PSRAM=%lu KB, Internal=%lu KB",
             (frame_buffer_size + cache_buffer_size) * 2 / 1024, line_buffer_size * 2 / 1024);

    // 检查内存泄漏迹象
    static size_t last_free_internal = 0;
    if (last_free_internal > 0) {
        int32_t diff = (int32_t)free_internal - (int32_t)last_free_internal;
        if (diff < -1000) { // 如果减少超过1KB
            ESP_LOGW(TAG, "  ⚠️  Internal RAM decreased by %ld bytes since last check", -diff);
        } else if (diff > 1000) {
            ESP_LOGI(TAG, "  ✅ Internal RAM increased by %ld bytes since last check", diff);
        }
    }
    last_free_internal = free_internal;
}

// 表情映射表 - 将各种表情映射到src目录下的实际GIF资源
const EyeGifDrawDisplay::EmotionMap EyeGifDrawDisplay::emotion_maps_[] = {
    // 中性/平静类表情 -> staticstate
    {"neutral", &staticstate},
    {"relaxed", &staticstate},
    {"sleepy", &staticstate},

    // 积极/开心类表情 -> happy
    {"happy", &happy},
    {"laughing", &happy},
    {"funny", &happy},
    {"loving", &cute},
    {"confident", &happy},
    {"winking", &moving},
    {"cool", &happy},
    {"delicious", &cute},
    {"kissy", &cute},
    {"silly", &happy},

    // 悲伤类表情 -> sad
    {"sad", &sad},
    {"crying", &sad},

    // 愤怒类表情 -> anger
    {"angry", &scare},

    // 惊讶类表情 -> scare
    {"surprised", &scare},
    {"shocked", &scare},

    // 思考/困惑类表情 -> buxue
    {"thinking", &moving},
    {"confused", &buxue},
    {"embarrassed", &buxue},

    // 系统状态也映射到现有表情
    {"loading", &buxue},        // 加载中用思考表情
    {"listening", &staticstate}, // 听取中用静态表情
    {"speaking", &happy},       // 说话中用开心表情
    {"error", &anger},          // 错误用愤怒表情

    {"purple_left",&purple_eye_240_240},
    {"purple_right",&purple_eye_240_240_ro},
    {"heart_beat",&heart_beat},

    {nullptr, nullptr}  // 结束标记
};

EyeGifDrawDisplay::EyeGifDrawDisplay(int width, int height, int offset_x, int offset_y, bool mirror_x,
                                     bool mirror_y, bool swap_xy, DisplayFonts fonts)
    : Display(),
      width_(width), height_(height), offset_x_(offset_x), offset_y_(offset_y),
      mirror_x_(mirror_x), mirror_y_(mirror_y), swap_xy_(swap_xy), fonts_(fonts) {
    ESP_LOGI(TAG, "EyeGifDrawDisplay dual screen constructor called");

    // 创建互斥锁
    mutex_ = xSemaphoreCreateMutex();
    if (mutex_ == nullptr) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return;
    }

    // 创建GIF渲染队列
    render_queue_ = xQueueCreate(GIF_SYNC_QUEUE_SIZE, sizeof(GifRenderMessage));
    if (render_queue_ == nullptr) {
        ESP_LOGE(TAG, "Failed to create GIF render queue");
        return;
    }

    // 初始化面板和GIF状态
    InitializePanels();
    InitializeGifStates();

    // 启动GIF渲染任务
    StartGifRenderTask();

    ESP_LOGI(TAG, "EyeGifDrawDisplay initialization completed");

    // 测试：先绘制纯色来验证LCD是否工作
    ESP_LOGI(TAG, "🧪 Testing LCD with solid color");
    TestLcdWithSolidColor();

    // 延迟2秒后加载GIF
    vTaskDelay(pdMS_TO_TICKS(2000));
    ESP_LOGI(TAG, "🧪 Testing GIF loading after LCD test");
    SetEmotion("neutral");
}

EyeGifDrawDisplay::~EyeGifDrawDisplay() {
    ESP_LOGI(TAG, "EyeGifDrawDisplay destructor called");

    // 停止GIF渲染任务
    StopGifRenderTask();

    // 清理GIF状态
    CleanupGifState(&gif_state1_);
    CleanupGifState(&gif_state2_);

    // 清理GIF渲染队列
    if (render_queue_) {
        vQueueDelete(render_queue_);
        render_queue_ = nullptr;
    }

    // 清理互斥锁
    if (mutex_) {
        vSemaphoreDelete(mutex_);
        mutex_ = nullptr;
    }
}

void EyeGifDrawDisplay::InitializePanels() {
    ESP_LOGI(TAG, "=== Initializing LCD panels ===");

    // 获取外部屏幕句柄
    extern esp_lcd_panel_io_handle_t lcd_io_eye;
    extern esp_lcd_panel_handle_t lcd_panel_eye;
    extern esp_lcd_panel_io_handle_t lcd_io_eye2;
    extern esp_lcd_panel_handle_t lcd_panel_eye2;

    ESP_LOGI(TAG, "=== External handles debug ===");
    ESP_LOGI(TAG, "lcd_io_eye: %p, lcd_panel_eye: %p", lcd_io_eye, lcd_panel_eye);
    ESP_LOGI(TAG, "lcd_io_eye2: %p, lcd_panel_eye2: %p", lcd_io_eye2, lcd_panel_eye2);

    panel_io1_ = lcd_io_eye;
    panel1_ = lcd_panel_eye;
    panel_io2_ = lcd_io_eye2;
    panel2_ = lcd_panel_eye2;

    ESP_LOGI(TAG, "=== Assigned handles debug ===");
    ESP_LOGI(TAG, "panel_io1_: %p, panel1_: %p", panel_io1_, panel1_);
    ESP_LOGI(TAG, "panel_io2_: %p, panel2_: %p", panel_io2_, panel2_);

    // 验证屏幕句柄
    if (!panel_io1_ || !panel1_) {
        ESP_LOGE(TAG, "❌ Screen 1 handles are invalid - panel_io1_: %p, panel1_: %p", panel_io1_, panel1_);
        ESP_LOGE(TAG, "❌ This will cause GIF rendering to fail!");
        return;
    }
    if (!panel_io2_ || !panel2_) {
        ESP_LOGE(TAG, "❌ Screen 2 handles are invalid - panel_io2_: %p, panel2_: %p", panel_io2_, panel2_);
        ESP_LOGE(TAG, "❌ This will cause GIF rendering to fail!");
        return;
    }

    ESP_LOGI(TAG, "✅ All screen handles validated successfully");
    ESP_LOGI(TAG, "=== LCD panels initialized successfully ===");
}

void EyeGifDrawDisplay::InitializeGifStates() {
    ESP_LOGI(TAG, "=== Initializing GIF states with AnimatedGIF ===");
    log_memory_usage();

    // 初始化屏幕1的GIF状态
    memset(&gif_state1_, 0, sizeof(GifPlayState));

    // 分配AnimatedGIF解码器
    gif_state1_.gif_decoder = new AnimatedGIF();
    if (!gif_state1_.gif_decoder) {
        ESP_LOGE(TAG, "Failed to create AnimatedGIF decoder for screen 1");
        return;
    }

    // 分配帧缓冲区
    gif_state1_.frame_buffer = AllocateFrameBuffer();
    if (!gif_state1_.frame_buffer) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer for screen 1");
        delete gif_state1_.gif_decoder;
        gif_state1_.gif_decoder = nullptr;
        return;
    }

    // 分配行缓冲区（仅N行，大大减少内部RAM占用）
    gif_state1_.line_buffer = AllocateLineBuffer();
    gif_state1_.line_buffer_size = width_ * GIF_LINES_PER_BATCH * sizeof(uint16_t);
    if (!gif_state1_.line_buffer) {
        ESP_LOGE(TAG, "Failed to allocate line buffer for screen 1");
        FreeFrameBuffer(gif_state1_.frame_buffer);
        delete gif_state1_.gif_decoder;
        gif_state1_.gif_decoder = nullptr;
        gif_state1_.frame_buffer = nullptr;
        return;
    }

    // 强制禁用Turbo模式（使用非turbo模式）
    gif_state1_.turbo_buffer = nullptr;
    gif_state1_.turbo_enabled = false;
    ESP_LOGI(TAG, "Non-turbo mode set for screen 1");

    // 初始化帧缓存
    InitializeFrameCache(&gif_state1_);

    // 初始化屏幕2的GIF状态
    memset(&gif_state2_, 0, sizeof(GifPlayState));

    // 分配AnimatedGIF解码器
    gif_state2_.gif_decoder = new AnimatedGIF();
    if (!gif_state2_.gif_decoder) {
        ESP_LOGE(TAG, "Failed to create AnimatedGIF decoder for screen 2");
        CleanupGifState(&gif_state1_);
        return;
    }

    // 分配帧缓冲区
    gif_state2_.frame_buffer = AllocateFrameBuffer();
    if (!gif_state2_.frame_buffer) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer for screen 2");
        delete gif_state2_.gif_decoder;
        gif_state2_.gif_decoder = nullptr;
        CleanupGifState(&gif_state1_);
        return;
    }

    // 分配行缓冲区（仅N行，大大减少内部RAM占用）
    gif_state2_.line_buffer = AllocateLineBuffer();
    gif_state2_.line_buffer_size = width_ * GIF_LINES_PER_BATCH * sizeof(uint16_t);
    if (!gif_state2_.line_buffer) {
        ESP_LOGE(TAG, "Failed to allocate line buffer for screen 2");
        FreeFrameBuffer(gif_state2_.frame_buffer);
        delete gif_state2_.gif_decoder;
        gif_state2_.gif_decoder = nullptr;
        gif_state2_.frame_buffer = nullptr;
        CleanupGifState(&gif_state1_);
        return;
    }

    // 强制禁用Turbo模式（使用非turbo模式）
    gif_state2_.turbo_buffer = nullptr;
    gif_state2_.turbo_enabled = false;
    ESP_LOGI(TAG, "Non-turbo mode set for screen 2");

    // 初始化帧缓存
    InitializeFrameCache(&gif_state2_);

    ESP_LOGI(TAG, "=== GIF states initialized successfully ===");
    ESP_LOGI(TAG, "Screen 1: frame_buffer=%p, line_buffer=%p (%lu lines), turbo=%d",
             gif_state1_.frame_buffer, gif_state1_.line_buffer, GIF_LINES_PER_BATCH, gif_state1_.turbo_enabled);
    ESP_LOGI(TAG, "Screen 2: frame_buffer=%p, line_buffer=%p (%lu lines), turbo=%d",
             gif_state2_.frame_buffer, gif_state2_.line_buffer, GIF_LINES_PER_BATCH, gif_state2_.turbo_enabled);
    ESP_LOGI(TAG, "Internal RAM savings: %ld bytes per screen (was %d×%d, now %d×%lu)",
             (long)((width_ * height_ - width_ * GIF_LINES_PER_BATCH) * sizeof(uint16_t)),
             width_, height_, width_, GIF_LINES_PER_BATCH);
    log_memory_usage();
}

bool EyeGifDrawDisplay::Lock(int timeout_ms) {
    if (mutex_ == nullptr) {
        return false;
    }
    return xSemaphoreTake(mutex_, pdMS_TO_TICKS(timeout_ms)) == pdTRUE;
}

void EyeGifDrawDisplay::Unlock() {
    if (mutex_ != nullptr) {
        xSemaphoreGive(mutex_);
    }
}

const lv_img_dsc_t* EyeGifDrawDisplay::GetGifResource(const char* emotion) {
    if (!emotion) {
        return &staticstate;
    }

    // 查找对应的GIF资源
    for (const auto& map : emotion_maps_) {
        if (map.name && strcmp(map.name, emotion) == 0) {
            return map.gif ? map.gif : &staticstate;
        }
    }

    // 如果没找到对应表情，使用默认表情
    ESP_LOGW(TAG, "Unknown emotion '%s', using default", emotion);
    return &staticstate;
}

esp_lcd_panel_handle_t EyeGifDrawDisplay::GetPanelHandle(uint8_t screen_id) {
    return (screen_id == 1) ? panel1_ : panel2_;
}

EyeGifDrawDisplay::GifPlayState* EyeGifDrawDisplay::GetGifState(uint8_t screen_id) {
    return (screen_id == 1) ? &gif_state1_ : &gif_state2_;
}

void EyeGifDrawDisplay::CleanupGifState(GifPlayState* state) {
    if (!state) return;

    ESP_LOGI(TAG, "Cleaning up GIF state");

    // 清理AnimatedGIF解码器
    if (state->gif_decoder) {
        state->gif_decoder->close();
        delete state->gif_decoder;
        state->gif_decoder = nullptr;
    }

    // 清理帧缓存
    DestroyFrameCache(state);

    // 清理缓冲区
    if (state->frame_buffer) {
        FreeFrameBuffer(state->frame_buffer);
        state->frame_buffer = nullptr;
    }

    if (state->line_buffer) {
        FreeLineBuffer(state->line_buffer);
        state->line_buffer = nullptr;
    }

    if (state->turbo_buffer) {
        FreeTurboBuffer(state->turbo_buffer);
        state->turbo_buffer = nullptr;
    }

    memset(state, 0, sizeof(GifPlayState));
}

uint32_t EyeGifDrawDisplay::GetCurrentTimeMs() {
    return esp_timer_get_time() / 1000;
}

void EyeGifDrawDisplay::TestLcdWithSolidColor() {
    ESP_LOGI(TAG, "🧪 Testing LCD panels with solid colors");

    // 创建测试缓冲区
    uint16_t* test_buffer = (uint16_t*)heap_caps_malloc(width_ * 8 * sizeof(uint16_t), MALLOC_CAP_INTERNAL);
    if (!test_buffer) {
        ESP_LOGE(TAG, "Failed to allocate test buffer");
        return;
    }

    // 测试屏幕1 - 红色
    ESP_LOGI(TAG, "🔴 Drawing red color to screen 1");
    for (int i = 0; i < width_ * 8; i++) {
        test_buffer[i] = 0xF800; // 红色 RGB565
    }

    for (int y = 0; y < height_; y += 8) {
        int lines = (y + 8 > height_) ? (height_ - y) : 8;
        esp_err_t ret = esp_lcd_panel_draw_bitmap(panel1_, 0, y, width_, y + lines, test_buffer);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to draw red to screen 1: %s", esp_err_to_name(ret));
        }
    }

    // 测试屏幕2 - 蓝色
    ESP_LOGI(TAG, "🔵 Drawing blue color to screen 2");
    for (int i = 0; i < width_ * 8; i++) {
        test_buffer[i] = 0x001F; // 蓝色 RGB565
    }

    for (int y = 0; y < height_; y += 8) {
        int lines = (y + 8 > height_) ? (height_ - y) : 8;
        esp_err_t ret = esp_lcd_panel_draw_bitmap(panel2_, 0, y, width_, y + lines, test_buffer);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to draw blue to screen 2: %s", esp_err_to_name(ret));
        }
    }

    heap_caps_free(test_buffer);
    ESP_LOGI(TAG, "🧪 LCD test completed - Screen 1 should be red, Screen 2 should be blue");
}

uint16_t* EyeGifDrawDisplay::AllocateFrameBuffer() {
    size_t buffer_size = width_ * height_ * sizeof(uint16_t);

    uint16_t* buffer = nullptr;
    if (GIF_USE_PSRAM_BUFFER) {
        buffer = (uint16_t*)heap_caps_malloc(buffer_size, MALLOC_CAP_SPIRAM);
        ESP_LOGI(TAG, "Allocated %ld bytes frame buffer in PSRAM: %p", (long)buffer_size, buffer);
    } else {
        buffer = (uint16_t*)heap_caps_malloc(buffer_size, MALLOC_CAP_INTERNAL | MALLOC_CAP_DMA);
        ESP_LOGI(TAG, "Allocated %ld bytes frame buffer in Internal RAM: %p", (long)buffer_size, buffer);
    }

    if (!buffer) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer of %ld bytes", (long)buffer_size);
    }

    return buffer;
}

void EyeGifDrawDisplay::FreeFrameBuffer(uint16_t* buffer) {
    if (buffer) {
        heap_caps_free(buffer);
    }
}

uint8_t* EyeGifDrawDisplay::AllocateTurboBuffer() {
    // AnimatedGIF Turbo缓冲区大小
    size_t turbo_size = TURBO_BUFFER_SIZE + (width_ * height_);

    uint8_t* buffer = nullptr;
    if (GIF_USE_PSRAM_BUFFER) {
        buffer = (uint8_t*)heap_caps_malloc(turbo_size, MALLOC_CAP_SPIRAM);
        ESP_LOGI(TAG, "Allocated %ld bytes turbo buffer in PSRAM: %p", (long)turbo_size, buffer);
    } else {
        buffer = (uint8_t*)heap_caps_malloc(turbo_size, MALLOC_CAP_INTERNAL);
        ESP_LOGI(TAG, "Allocated %ld bytes turbo buffer in Internal RAM: %p", (long)turbo_size, buffer);
    }

    if (!buffer) {
        ESP_LOGW(TAG, "Failed to allocate turbo buffer of %ld bytes", (long)turbo_size);
    }

    return buffer;
}

void EyeGifDrawDisplay::FreeTurboBuffer(uint8_t* buffer) {
    if (buffer) {
        heap_caps_free(buffer);
    }
}

uint16_t* EyeGifDrawDisplay::AllocateLineBuffer() {
    // 只分配N行的缓冲区，大大减少内部RAM占用
    size_t buffer_size = width_ * GIF_LINES_PER_BATCH * sizeof(uint16_t);

    uint16_t* buffer = nullptr;
    // 行缓冲区需要在内部RAM中，且满足DMA对齐要求
    buffer = (uint16_t*)heap_caps_malloc(buffer_size, MALLOC_CAP_INTERNAL | MALLOC_CAP_DMA);
    ESP_LOGI(TAG, "Allocated %ld bytes line buffer (%lu lines) in Internal RAM: %p",
             (long)buffer_size, GIF_LINES_PER_BATCH, buffer);

    if (!buffer) {
        ESP_LOGE(TAG, "Failed to allocate line buffer of %ld bytes", (long)buffer_size);
    }

    return buffer;
}

void EyeGifDrawDisplay::FreeLineBuffer(uint16_t* buffer) {
    if (buffer) {
        heap_caps_free(buffer);
    }
}

void EyeGifDrawDisplay::InitializeFrameCache(GifPlayState* state) {
    if (!state) return;

    ESP_LOGI(TAG, "Initializing 10-frame PSRAM cache");

    // 创建缓存互斥锁
    state->cache_mutex = xSemaphoreCreateMutex();
    if (!state->cache_mutex) {
        ESP_LOGE(TAG, "Failed to create cache mutex");
        return;
    }

    // 初始化帧缓存数组
    for (int i = 0; i < 10; i++) {
        state->frame_cache[i].frame_data = AllocateFrameBuffer();
        if (!state->frame_cache[i].frame_data) {
            ESP_LOGE(TAG, "Failed to allocate cache frame %d", i);
            // 清理已分配的缓存
            for (int j = 0; j < i; j++) {
                FreeFrameBuffer(state->frame_cache[j].frame_data);
                state->frame_cache[j].frame_data = nullptr;
            }
            vSemaphoreDelete(state->cache_mutex);
            state->cache_mutex = nullptr;
            return;
        }

        state->frame_cache[i].frame_index = UINT32_MAX;
        state->frame_cache[i].delay_ms = 0;
        state->frame_cache[i].is_valid = false;
        state->frame_cache[i].last_access = 0;
    }

    state->cache_start_frame = 0;
    state->cache_count = 0;

    ESP_LOGI(TAG, "Frame cache initialized with 10 frames");
}

void EyeGifDrawDisplay::DestroyFrameCache(GifPlayState* state) {
    if (!state) return;

    ESP_LOGI(TAG, "Destroying frame cache");

    // 清理帧缓存
    for (int i = 0; i < 10; i++) {
        if (state->frame_cache[i].frame_data) {
            FreeFrameBuffer(state->frame_cache[i].frame_data);
            state->frame_cache[i].frame_data = nullptr;
        }
        state->frame_cache[i].is_valid = false;
    }

    // 清理互斥锁
    if (state->cache_mutex) {
        vSemaphoreDelete(state->cache_mutex);
        state->cache_mutex = nullptr;
    }

    state->cache_count = 0;
}

esp_err_t EyeGifDrawDisplay::CacheFrame(GifPlayState* state, uint32_t frame_index,
                                       uint16_t* frame_data, uint32_t delay_ms) {
    if (!state || !frame_data || !state->cache_mutex) {
        ESP_LOGE(TAG, "❌ CacheFrame invalid args: state=%p, frame_data=%p, cache_mutex=%p",
                 state, frame_data, state ? state->cache_mutex : nullptr);
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "🗂️ CacheFrame: Attempting to cache frame %lu", frame_index);

    if (xSemaphoreTake(state->cache_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGE(TAG, "❌ CacheFrame: Failed to take mutex for frame %lu", frame_index);
        return ESP_ERR_TIMEOUT;
    }

    // 查找空闲或最旧的缓存槽
    int target_slot = -1;
    uint32_t oldest_access = UINT32_MAX;

    for (int i = 0; i < 10; i++) {
        if (!state->frame_cache[i].is_valid) {
            target_slot = i;
            break;
        }

        if (state->frame_cache[i].last_access < oldest_access) {
            oldest_access = state->frame_cache[i].last_access;
            target_slot = i;
        }
    }

    if (target_slot >= 0) {
        // 复制帧数据
        memcpy(state->frame_cache[target_slot].frame_data, frame_data,
               width_ * height_ * sizeof(uint16_t));

        state->frame_cache[target_slot].frame_index = frame_index;
        state->frame_cache[target_slot].delay_ms = delay_ms;
        state->frame_cache[target_slot].is_valid = true;
        state->frame_cache[target_slot].last_access = GetCurrentTimeMs();

        if (state->cache_count < 10) {
            state->cache_count++;
        }

        ESP_LOGD(TAG, "✅ Cached frame %lu in slot %d (cache_count: %lu)", frame_index, target_slot, state->cache_count);
    }

    xSemaphoreGive(state->cache_mutex);
    return (target_slot >= 0) ? ESP_OK : ESP_FAIL;
}

EyeGifDrawDisplay::FrameCache* EyeGifDrawDisplay::GetCachedFrame(GifPlayState* state, uint32_t frame_index) {
    if (!state || !state->cache_mutex) {
        return nullptr;
    }

    if (xSemaphoreTake(state->cache_mutex, pdMS_TO_TICKS(10)) != pdTRUE) {
        return nullptr;
    }

    FrameCache* result = nullptr;
    ESP_LOGD(TAG, "🔍 Searching cache for frame %lu (cache_count: %lu)", frame_index, state->cache_count);

    for (int i = 0; i < 10; i++) {
        if (state->frame_cache[i].is_valid) {
            ESP_LOGD(TAG, "  Slot %d: frame %lu (valid)", i, state->frame_cache[i].frame_index);
            if (state->frame_cache[i].frame_index == frame_index) {
                state->frame_cache[i].last_access = GetCurrentTimeMs();
                result = &state->frame_cache[i];
                ESP_LOGV(TAG, "🎯 Cache HIT for frame %lu in slot %d", frame_index, i);
                break;
            }
        } else {
            ESP_LOGD(TAG, "  Slot %d: empty", i);
        }
    }

    if (!result) {
        ESP_LOGV(TAG, "❌ Cache MISS for frame %lu", frame_index);
    }

    xSemaphoreGive(state->cache_mutex);
    return result;
}

void EyeGifDrawDisplay::PreloadFrames(GifPlayState* state, uint32_t start_frame) {
    if (!state || !state->gif_decoder || !state->cache_mutex) {
        return;
    }

    ESP_LOGI(TAG, "🔄 Smart preloading frames starting from frame %lu", start_frame);

    // 智能预加载策略：
    // 1. 如果缓存为空，从当前位置连续解码10帧
    // 2. 如果缓存部分满，补充到10帧
    // 3. 使用滑动窗口，当播放接近缓存边界时预加载新帧

    uint32_t frames_to_preload = 0;
    uint32_t current_cache_end = (state->cache_start_frame + state->cache_count) % state->total_frames;

    if (state->cache_count == 0) {
        // 缓存为空，预加载10帧
        frames_to_preload = (state->total_frames < 10) ? state->total_frames : 10;
        ESP_LOGI(TAG, "📦 Cache empty, preloading %lu frames", frames_to_preload);
    } else if (state->cache_count < 10) {
        // 缓存未满，补充到10帧
        uint32_t remaining_cache_slots = 10 - state->cache_count;
        uint32_t remaining_frames = state->total_frames - state->cache_count;
        frames_to_preload = (remaining_frames < remaining_cache_slots) ? remaining_frames : remaining_cache_slots;
        ESP_LOGI(TAG, "📦 Cache partial (%lu/10), preloading %lu more frames",
                 state->cache_count, frames_to_preload);
    } else {
        // 缓存已满，检查是否需要滑动窗口
        uint32_t distance_to_end = (current_cache_end >= start_frame) ?
                                   (current_cache_end - start_frame) :
                                   (state->total_frames - start_frame + current_cache_end);

        if (distance_to_end <= 3) {
            // 接近缓存边界，预加载新帧并淘汰旧帧
            frames_to_preload = 5; // 预加载5帧
            ESP_LOGI(TAG, "🔄 Sliding window: distance_to_end=%lu, preloading %lu frames",
                     distance_to_end, frames_to_preload);
        }
    }

    // 执行预加载
    for (uint32_t i = 0; i < frames_to_preload; i++) {
        uint32_t target_frame = (current_cache_end + i) % state->total_frames;

        // 检查是否已经缓存
        if (GetCachedFrame(state, target_frame)) {
            ESP_LOGD(TAG, "Frame %lu already cached, skipping", target_frame);
            continue;
        }

        ESP_LOGD(TAG, "🎬 Preloading frame %lu", target_frame);

        // 实现真正的预解码逻辑
        esp_err_t preload_result = PreloadSingleFrame(state, target_frame);
        if (preload_result != ESP_OK) {
            ESP_LOGW(TAG, "⚠️ Failed to preload frame %lu: %s", target_frame, esp_err_to_name(preload_result));
        }
    }
}

esp_err_t EyeGifDrawDisplay::PreloadSingleFrame(GifPlayState* state, uint32_t target_frame) {
    if (!state || !state->gif_decoder || !state->frame_buffer) {
        return ESP_ERR_INVALID_ARG;
    }

    // 检查是否已经缓存
    if (GetCachedFrame(state, target_frame)) {
        ESP_LOGD(TAG, "Frame %lu already cached", target_frame);
        return ESP_OK;
    }

    ESP_LOGD(TAG, "🎬 Preloading frame %lu", target_frame);

    // 创建AnimatedGIF上下文
    AnimatedGIFContext gif_context = {
        .display = this,
        .screen_id = 0, // 预加载时不关心具体屏幕
        .current_frame_buffer = state->frame_buffer,
        .frame_width = (uint32_t)width_,
        .frame_height = (uint32_t)height_
    };

    // 清空帧缓冲区
    memset(state->frame_buffer, 0, width_ * height_ * sizeof(uint16_t));

    // 解码帧
    int delay_ms_int = 0;
    int result = state->gif_decoder->playFrame(true, &delay_ms_int, &gif_context);

    if (result != 1) { // AnimatedGIF返回1表示成功
        ESP_LOGW(TAG, "Failed to preload frame %lu, result: %d", target_frame, result);
        return ESP_FAIL;
    }

    uint32_t frame_delay = (delay_ms_int > 0) ? delay_ms_int : 100;

    // 缓存解码的帧
    esp_err_t cache_result = CacheFrame(state, target_frame, state->frame_buffer, frame_delay);
    if (cache_result != ESP_OK) {
        ESP_LOGW(TAG, "Failed to cache preloaded frame %lu: %s", target_frame, esp_err_to_name(cache_result));
        return cache_result;
    }

    ESP_LOGV(TAG, "✅ Successfully preloaded and cached frame %lu", target_frame);
    return ESP_OK;
}

void EyeGifDrawDisplay::EvictOldFrames(GifPlayState* state) {
    if (!state || !state->cache_mutex) {
        return;
    }

    if (xSemaphoreTake(state->cache_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return;
    }

    uint32_t current_time = GetCurrentTimeMs();
    uint32_t evict_threshold = 30000; // 30秒未访问的帧将被淘汰

    for (int i = 0; i < 10; i++) {
        if (state->frame_cache[i].is_valid &&
            (current_time - state->frame_cache[i].last_access) > evict_threshold) {

            ESP_LOGD(TAG, "Evicting old frame %lu from slot %d",
                     state->frame_cache[i].frame_index, i);

            state->frame_cache[i].is_valid = false;
            state->frame_cache[i].frame_index = UINT32_MAX;

            if (state->cache_count > 0) {
                state->cache_count--;
            }
        }
    }

    xSemaphoreGive(state->cache_mutex);
}

void EyeGifDrawDisplay::UpdateCacheStats(GifPlayState* state, bool cache_hit) {
    if (!state) return;

    uint32_t current_time = GetCurrentTimeMs();

    if (cache_hit) {
        state->cache_stats.cache_hits++;
    } else {
        state->cache_stats.cache_misses++;
    }

    // 每2秒打印一次统计信息
    if (current_time - state->cache_stats.last_stats_time >= 2000) {
        uint32_t total_requests = state->cache_stats.cache_hits + state->cache_stats.cache_misses;
        if (total_requests > 0) {
            float hit_rate = (float)state->cache_stats.cache_hits / total_requests * 100.0f;
            ESP_LOGI(TAG, "📊 Cache Stats (2s): Hits=%lu, Misses=%lu, Hit Rate=%.1f%%, Cached Frames=%lu",
                     state->cache_stats.cache_hits,
                     state->cache_stats.cache_misses,
                     hit_rate,
                     state->cache_count);
        }

        // 重置统计
        state->cache_stats.cache_hits = 0;
        state->cache_stats.cache_misses = 0;
        state->cache_stats.last_stats_time = current_time;
    }
}

void EyeGifDrawDisplay::PrintCacheStats(GifPlayState* state, uint8_t screen_id) {
    if (!state) return;

    uint32_t total_requests = state->cache_stats.cache_hits + state->cache_stats.cache_misses;
    if (total_requests > 0) {
        float hit_rate = (float)state->cache_stats.cache_hits / total_requests * 100.0f;
        ESP_LOGI(TAG, "📊 Screen %u Cache Summary: Hits=%lu, Misses=%lu, Hit Rate=%.1f%%, Cached=%lu/10",
                 screen_id,
                 state->cache_stats.cache_hits,
                 state->cache_stats.cache_misses,
                 hit_rate,
                 state->cache_count);
    }
}

esp_err_t EyeGifDrawDisplay::LoadGifOnScreen(uint8_t screen_id, const lv_img_dsc_t* gif_resource) {
    if (!gif_resource || screen_id < 1 || screen_id > 2) {
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Loading GIF on screen %u with AnimatedGIF: %p", screen_id, gif_resource);

    GifPlayState* state = GetGifState(screen_id);
    if (!state || !state->frame_buffer || !state->gif_decoder) {
        ESP_LOGE(TAG, "Invalid GIF state for screen %u", screen_id);
        return ESP_ERR_INVALID_STATE;
    }

    // 如果已经在播放相同的GIF，不需要重新加载
    if (state->gif_resource == gif_resource && state->is_playing) {
        ESP_LOGI(TAG, "Same GIF already loaded on screen %u", screen_id);
        return ESP_OK;
    }

    ESP_LOGI(TAG, "🔄 Loading new GIF on screen %u - current_resource: %p, new_resource: %p, is_playing: %d",
             screen_id, state->gif_resource, gif_resource, state->is_playing);

    // 关闭旧的GIF
    if (state->gif_resource) {
        state->gif_decoder->close();
    }

    // 初始化AnimatedGIF
    state->gif_decoder->begin(GIF_PALETTE_RGB565_LE);

    // 设置自定义分配器，强制使用PSRAM
    ESP_LOGI(TAG, "🧠 Setting custom PSRAM allocator for AnimatedGIF");

    // 设置绘制类型为RAW模式（非turbo模式必须使用RAW）
    int draw_type_result = state->gif_decoder->setDrawType(GIF_DRAW_RAW);
    ESP_LOGI(TAG, "🎨 Set draw type to RAW (non-turbo mode): result=%d", draw_type_result);

    // 非turbo模式：使用回调函数而不是直接帧缓冲区
    ESP_LOGI(TAG, "Non-turbo mode enabled for screen %u (using callback)", screen_id);

    // 打开GIF数据
    ESP_LOGI(TAG, "🔓 Opening GIF: data=%p, size=%lu, callback=%p",
             gif_resource->data, gif_resource->data_size, GlobalAnimatedGIFDrawCallback);

    int result = state->gif_decoder->open((uint8_t*)gif_resource->data,
                                         gif_resource->data_size,
                                         GlobalAnimatedGIFDrawCallback);

    ESP_LOGI(TAG, "🔓 GIF open result: %d (1=success)", result);

    if (result != 1) { // AnimatedGIF返回1表示成功
        ESP_LOGE(TAG, "Failed to open GIF with AnimatedGIF for screen %u: %d", screen_id, result);
        return ESP_ERR_INVALID_ARG;
    }

    // 获取GIF信息
    uint32_t width = state->gif_decoder->getCanvasWidth();
    uint32_t height = state->gif_decoder->getCanvasHeight();

    // 获取GIF详细信息
    GIFINFO gif_info;
    if (state->gif_decoder->getInfo(&gif_info) == 1) {
        state->total_frames = gif_info.iFrameCount;
        ESP_LOGI(TAG, "GIF info: %ld frames, duration: %ld ms",
                 (long)gif_info.iFrameCount, (long)gif_info.iDuration);
    } else {
        state->total_frames = 50; // 默认估算值
        ESP_LOGW(TAG, "Could not get GIF info, using estimated frame count");
    }

    // 更新状态
    state->gif_resource = gif_resource;
    state->current_frame = 0;
    state->last_update_time = GetCurrentTimeMs();
    state->frame_delay = 100; // 默认100ms延迟
    state->is_playing = true;
    state->needs_update = true;

    // 清空帧缓存并重置缓存状态
    state->cache_count = 0;
    state->cache_start_frame = 0;

    // 初始化缓存统计
    state->cache_stats.cache_hits = 0;
    state->cache_stats.cache_misses = 0;
    state->cache_stats.frames_cached = 0;
    state->cache_stats.last_stats_time = GetCurrentTimeMs();

    ESP_LOGI(TAG, "✅ GIF loaded on screen %d: %lux%lu, %lu frames",
             screen_id, width, height, state->total_frames);
    ESP_LOGI(TAG, "🎬 Screen %d GIF state: is_playing=%d, needs_update=%d, current_frame=%lu",
             screen_id, state->is_playing, state->needs_update, state->current_frame);
    ESP_LOGI(TAG, "🔗 Screen %d GIF resource set: %p -> %p", screen_id, gif_resource, state->gif_resource);

    // 立即开始预加载帧以建立初始缓存
    ESP_LOGI(TAG, "🚀 Starting initial frame preloading for screen %d", screen_id);
    PreloadFrames(state, 0);

    // 立即验证状态是否正确设置
    ESP_LOGI(TAG, "🔍 Immediate verification - Screen %d: decoder=%p, resource=%p, is_playing=%d",
             screen_id, state->gif_decoder, state->gif_resource, state->is_playing);

    return ESP_OK;
}

esp_err_t EyeGifDrawDisplay::UpdateGifFrame(uint8_t screen_id) {
    GifPlayState* state = GetGifState(screen_id);
    if (!state || !state->gif_decoder || !state->is_playing) {
        ESP_LOGD(TAG, "❌ UpdateGifFrame failed for screen %u - state: %p, decoder: %p, is_playing: %d",
                 screen_id, state, state ? state->gif_decoder : nullptr, state ? state->is_playing : 0);
        return ESP_ERR_INVALID_STATE;
    }

    uint32_t current_time = GetCurrentTimeMs();

    // 检查是否需要更新帧
    if (!state->needs_update && (current_time - state->last_update_time) < state->frame_delay) {
        ESP_LOGV(TAG, "⏰ Frame update not needed for screen %u (time: %lu, last: %lu, delay: %lu)",
                 screen_id, current_time, state->last_update_time, state->frame_delay);
        return ESP_OK; // 还没到更新时间
    }


    // 智能缓存策略：检查是否需要预加载更多帧
    uint32_t cache_end_frame = (state->cache_start_frame + state->cache_count) % state->total_frames;

    // 如果即将播放的帧接近缓存边界，触发预加载
    if (state->cache_count > 0) {
        uint32_t distance_to_cache_end = (cache_end_frame >= state->current_frame) ?
                                         (cache_end_frame - state->current_frame) :
                                         (state->total_frames - state->current_frame + cache_end_frame);

        if (distance_to_cache_end <= 3) {
            ESP_LOGI(TAG, "🔄 Approaching cache boundary (distance: %lu), triggering preload", distance_to_cache_end);
            PreloadFrames(state, state->current_frame);
        }
    }

    // 首先检查帧缓存
    FrameCache* cached_frame = GetCachedFrame(state, state->current_frame);

    if (cached_frame) {
        // 使用缓存的帧
        UpdateCacheStats(state, true); // 记录缓存命中

        // 直接从PSRAM缓存渲染（分行渲染）
        esp_err_t ret = RenderFrameToScreen(screen_id, cached_frame->frame_data);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to render cached frame to screen %u: %s",
                     screen_id, esp_err_to_name(ret));
            return ret;
        }

        state->frame_delay = cached_frame->delay_ms;
    } else {
        // 需要解码新帧
        UpdateCacheStats(state, false); // 记录缓存未命中

        // 创建AnimatedGIF上下文
        AnimatedGIFContext gif_context = {
            .display = this,
            .screen_id = screen_id,
            .current_frame_buffer = state->frame_buffer,
            .frame_width = (uint32_t)width_,
            .frame_height = (uint32_t)height_
        };

        // 清空帧缓冲区
        memset(state->frame_buffer, 0, width_ * height_ * sizeof(uint16_t));

        // 解码帧
        int delay_ms_int = 0;
        int result = state->gif_decoder->playFrame(true, &delay_ms_int, &gif_context);
        // ESP_LOGI(TAG, "🎬 playFrame result: %d, delay: %d ms", result, delay_ms_int);

        if (result != 1) { // AnimatedGIF返回1表示成功
            if (state->current_frame > 0) {
                // 可能到达文件末尾，重置到开始
                state->gif_decoder->reset();
                state->current_frame = 0;
                state->needs_update = true;
                return ESP_OK;
            } else {
                ESP_LOGE(TAG, "Failed to decode frame %lu for screen %u, error: %d",
                         state->current_frame, screen_id, result);
                return ESP_FAIL;
            }
        }

        state->frame_delay = (delay_ms_int > 0) ? delay_ms_int : 100;

        // 缓存解码的帧
        ESP_LOGD(TAG, "🗂️ Caching frame %lu with delay %lu ms", state->current_frame, state->frame_delay);
        esp_err_t cache_result = CacheFrame(state, state->current_frame, state->frame_buffer, state->frame_delay);
        if (cache_result != ESP_OK) {
            ESP_LOGW(TAG, "❌ Failed to cache frame %lu: %s", state->current_frame, esp_err_to_name(cache_result));
        }

        // 直接从PSRAM帧缓冲区渲染（分行渲染）
        esp_err_t ret = RenderFrameToScreen(screen_id, state->frame_buffer);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to render decoded frame to screen %u: %s",
                     screen_id, esp_err_to_name(ret));
            return ret;
        }
    }

    // 更新状态
    state->last_update_time = current_time;
    state->needs_update = false;

    // 移动到下一帧
    state->current_frame++;
    if (state->current_frame >= state->total_frames) {
        state->current_frame = 0; // 循环播放
        // 重置AnimatedGIF到开始位置
        state->gif_decoder->reset();
    }

    ESP_LOGD(TAG, "Updated frame %lu/%lu on screen %d, delay=%lu ms",
             state->current_frame, state->total_frames, screen_id, state->frame_delay);

    return ESP_OK;
}

esp_err_t EyeGifDrawDisplay::RenderFrameToScreen(uint8_t screen_id, const uint16_t* frame_buffer) {
    if (!frame_buffer || screen_id < 1 || screen_id > 2) {
        ESP_LOGE(TAG, "❌ RenderFrameToScreen invalid args - screen_id: %u, frame_buffer: %p", screen_id, frame_buffer);
        return ESP_ERR_INVALID_ARG;
    }

    // 使用分行渲染来减少内部RAM占用
    esp_err_t ret = RenderFrameByLines(screen_id, frame_buffer);
    if (ret == ESP_OK) {
        ESP_LOGD(TAG, "✅ Frame rendered successfully to screen %u", screen_id);
    } else {
        ESP_LOGE(TAG, "❌ Frame rendering failed for screen %u: %s", screen_id, esp_err_to_name(ret));
    }
    return ret;
}

esp_err_t EyeGifDrawDisplay::RenderFrameByLines(uint8_t screen_id, const uint16_t* frame_buffer) {
    if (!frame_buffer || screen_id < 1 || screen_id > 2) {
        ESP_LOGE(TAG, "❌ Invalid arguments for RenderFrameByLines - screen_id: %u, frame_buffer: %p", screen_id, frame_buffer);
        return ESP_ERR_INVALID_ARG;
    }

    esp_lcd_panel_handle_t panel = GetPanelHandle(screen_id);
    if (!panel) {
        ESP_LOGE(TAG, "❌ Invalid panel handle for screen %u - panel: %p", screen_id, panel);
        ESP_LOGE(TAG, "❌ This means LCD panels were not properly initialized!");
        return ESP_ERR_INVALID_STATE;
    }

    GifPlayState* state = GetGifState(screen_id);
    if (!state || !state->line_buffer) {
        ESP_LOGE(TAG, "❌ Invalid line buffer for screen %u - state: %p, line_buffer: %p",
                 screen_id, state, state ? state->line_buffer : nullptr);
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGD(TAG, "Rendering frame to screen %u by lines (%lu lines per batch)", screen_id, GIF_LINES_PER_BATCH);

    // 分批渲染，每次处理N行
    for (uint32_t y = 0; y < height_; y += GIF_LINES_PER_BATCH) {
        uint32_t lines_to_render = GIF_LINES_PER_BATCH;

        // 处理最后一批可能不足N行的情况
        if (y + lines_to_render > height_) {
            lines_to_render = height_ - y;
        }

        // 从PSRAM复制N行数据到内部RAM的行缓冲区
        const uint16_t* src_line = &frame_buffer[y * width_];
        size_t copy_size = lines_to_render * width_ * sizeof(uint16_t);

        memcpy(state->line_buffer, src_line, copy_size);

        // 使用DMA渲染这N行到LCD面板
        ESP_LOGV(TAG, "📺 Drawing lines %lu-%lu to screen %u (panel: %p)", y, y + lines_to_render - 1, screen_id, panel);

        // 调试：检查第一行的前几个像素数据
        if (y == 50) {
            ESP_LOGI(TAG, "🔍 First line pixel data for screen %u: [0]=%04x [1]=%04x [2]=%04x [3]=%04x",
                     screen_id, state->line_buffer[100], state->line_buffer[101], state->line_buffer[102], state->line_buffer[103]);
        }

        esp_err_t ret = esp_lcd_panel_draw_bitmap(panel, 0, y, width_, y + lines_to_render, state->line_buffer);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "❌ Failed to draw lines %lu-%lu to screen %u: %s",
                     y, y + lines_to_render - 1, screen_id, esp_err_to_name(ret));
            return ret;
        }

        ESP_LOGV(TAG, "✅ Rendered lines %lu-%lu to screen %u", y, y + lines_to_render - 1, screen_id);
    }

    ESP_LOGD(TAG, "Frame rendered to screen %u successfully using line-by-line DMA", screen_id);
    return ESP_OK;
}

void EyeGifDrawDisplay::StartGifRenderTask() {
    if (render_task_handle_ != nullptr) {
        ESP_LOGW(TAG, "GIF render task already running");
        return;
    }

    // 在指定核心上创建渲染任务
    BaseType_t result = xTaskCreatePinnedToCore(
        GifRenderTask,
        "gif_render_task",
        GIF_SYNC_TASK_STACK,     // 堆栈大小
        this,                    // 参数
        GIF_SYNC_TASK_PRIORITY,  // 优先级
        &render_task_handle_,    // 任务句柄
        GIF_SYNC_TASK_CORE       // 绑定到指定核心
    );

    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create GIF render task");
        render_task_handle_ = nullptr;
    } else {
        ESP_LOGI(TAG, "GIF render task created successfully on core %d", (int)GIF_SYNC_TASK_CORE);
    }
}

void EyeGifDrawDisplay::StopGifRenderTask() {
    if (render_task_handle_ != nullptr) {
        vTaskDelete(render_task_handle_);
        render_task_handle_ = nullptr;
        ESP_LOGI(TAG, "GIF render task stopped");
    }
}

// GIF渲染任务实现
void EyeGifDrawDisplay::GifRenderTask(void* parameter) {
    EyeGifDrawDisplay* display = static_cast<EyeGifDrawDisplay*>(parameter);
    GifRenderMessage message;

    ESP_LOGI(TAG, "GIF render task started on core %d", (int)xPortGetCoreID());

    // 初始状态检查
    ESP_LOGI(TAG, "🔍 Initial GIF states check:");
    ESP_LOGI(TAG, "  Screen 1: decoder=%p, resource=%p, is_playing=%d",
             display->gif_state1_.gif_decoder, display->gif_state1_.gif_resource, display->gif_state1_.is_playing);
    ESP_LOGI(TAG, "  Screen 2: decoder=%p, resource=%p, is_playing=%d",
             display->gif_state2_.gif_decoder, display->gif_state2_.gif_resource, display->gif_state2_.is_playing);

    while (true) {
        // 处理队列中的渲染消息
        if (xQueueReceive(display->render_queue_, &message, pdMS_TO_TICKS(10)) == pdTRUE) {
            ESP_LOGI(TAG, "Processing GIF render message - screen: %u, sync_both: %d",
                     message.screen_id, message.sync_both_screens);

            if (!message.gif_resource) {
                ESP_LOGW(TAG, "Null GIF resource, skipping");
                continue;
            }

            if (message.sync_both_screens) {
                // 同步两个屏幕
                display->LoadGifOnScreen(1, message.gif_resource);
                display->LoadGifOnScreen(2, message.gif_resource);
            } else {
                // 单屏更新
                display->LoadGifOnScreen(message.screen_id, message.gif_resource);
            }
        }

        // 更新所有活跃的GIF帧
        if (display->gif_state1_.is_playing) {
            display->UpdateGifFrame(1);
        }
        if (display->gif_state2_.is_playing) {
            display->UpdateGifFrame(2);
        }

        // 短暂延时，控制帧率
        vTaskDelay(pdMS_TO_TICKS(GIF_LVGL_TIMER_PERIOD_MS));
    }
}

void EyeGifDrawDisplay::SetEmotionOnScreen(uint8_t screen_id, const char* emotion) {
    if (!emotion) {
        ESP_LOGW(TAG, "SetEmotionOnScreen called with null emotion");
        return;
    }

    ESP_LOGI(TAG, "Setting emotion '%s' on screen %u", emotion, screen_id);

    const lv_img_dsc_t* gif_resource = GetGifResource(emotion);
    if (!gif_resource) {
        ESP_LOGE(TAG, "Failed to get GIF resource for emotion: %s", emotion);
        return;
    }

    // 发送渲染消息到队列
    GifRenderMessage message = {
        .screen_id = screen_id,
        .gif_resource = gif_resource,
        .sync_both_screens = false
    };

    if (render_queue_ != nullptr) {
        if (xQueueSend(render_queue_, &message, pdMS_TO_TICKS(GIF_QUEUE_SEND_TIMEOUT_MS)) != pdTRUE) {
            ESP_LOGE(TAG, "Failed to send single screen message to queue");
        } else {
            ESP_LOGI(TAG, "Single screen message sent to queue successfully");
        }
    } else {
        ESP_LOGE(TAG, "GIF render queue not initialized");
    }
}

void EyeGifDrawDisplay::SetDualEmotion(const char* emotion1, const char* emotion2) {
    ESP_LOGI(TAG, "Setting dual emotions: screen1='%s', screen2='%s'",
             emotion1 ? emotion1 : "null", emotion2 ? emotion2 : "null");

    if (emotion1) {
        SetEmotionOnScreen(1, emotion1);
    }
    if (emotion2) {
        SetEmotionOnScreen(2, emotion2);
    }
}

void EyeGifDrawDisplay::SetSyncEmotion(const char* emotion) {
    ESP_LOGI(TAG, "Setting sync emotion: '%s'", emotion ? emotion : "null");

    if (!emotion) {
        return;
    }

    const lv_img_dsc_t* gif_resource = GetGifResource(emotion);
    if (!gif_resource) {
        ESP_LOGE(TAG, "Failed to get GIF resource for emotion: %s", emotion);
        return;
    }

    // 发送同步渲染消息到队列
    GifRenderMessage message = {
        .screen_id = 0,  // 忽略，因为sync_both_screens为true
        .gif_resource = gif_resource,
        .sync_both_screens = true
    };

    if (render_queue_ != nullptr) {
        if (xQueueSend(render_queue_, &message, pdMS_TO_TICKS(GIF_QUEUE_SEND_TIMEOUT_MS)) != pdTRUE) {
            ESP_LOGE(TAG, "Failed to send sync message to queue");
        } else {
            ESP_LOGI(TAG, "Sync message sent to queue successfully");
        }
    } else {
        ESP_LOGE(TAG, "GIF render queue not initialized");
    }
}

void EyeGifDrawDisplay::SetEmotion(const char* emotion) {
    if (!emotion) {
        ESP_LOGW(TAG, "SetEmotion called with null emotion");
        return;
    }

    ESP_LOGI(TAG, "Setting emotion on both screens: %s", emotion);
    log_memory_usage();

    // 根据表情类型在两个屏幕上显示不同的资源
    // 其他表情：两个屏幕显示相同内容
    SetSyncEmotion(emotion);

    log_memory_usage();
}

void EyeGifDrawDisplay::SetChatMessage(const char* role, const char* content) {
    // 空实现 - 魔眼只显示 GIF，不显示文字消息
    ESP_LOGD(TAG, "SetChatMessage ignored - EyeGifDrawDisplay only shows GIF animations");
}

void EyeGifDrawDisplay::SetIcon(const char* icon) {
    if (!icon) {
        return;
    }

    // 根据图标类型设置对应的表情，但不显示文字
    if (strcmp(icon, FONT_AWESOME_DOWNLOAD) == 0) {
        SetEmotion("loading");
        ESP_LOGI(TAG, "Icon: %s -> loading emotion", icon);
    } else if (strcmp(icon, FONT_AWESOME_WIFI) == 0) {
        SetEmotion("neutral");
        ESP_LOGI(TAG, "Icon: %s -> neutral emotion", icon);
    } else if (strcmp(icon, FONT_AWESOME_MUSIC) == 0) {
        SetEmotion("listening");
        ESP_LOGI(TAG, "Icon: %s -> listening emotion", icon);
    } else if (strcmp(icon, FONT_AWESOME_VOLUME_HIGH) == 0) {
        SetEmotion("speaking");
        ESP_LOGI(TAG, "Icon: %s -> speaking emotion", icon);
    } else {
        SetEmotion("neutral");
        ESP_LOGI(TAG, "Icon: %s -> neutral emotion (default)", icon);
    }
}

void EyeGifDrawDisplay::SetStatus(const char* status) {
    // 空实现 - 魔眼不显示状态文字
    ESP_LOGD(TAG, "SetStatus ignored - EyeGifDrawDisplay does not show status text");
}

void EyeGifDrawDisplay::SetTheme(const std::string& theme_name) {
    // 空实现 - 魔眼不支持主题切换
    ESP_LOGD(TAG, "SetTheme ignored - EyeGifDrawDisplay does not support theme switching");
}

// 调试函数：检查显示配置
void EyeGifDrawDisplay::DebugDisplayInfo() {
    ESP_LOGI(TAG, "=== Display Configuration Debug ===");

    ESP_LOGI(TAG, "Display parameters:");
    ESP_LOGI(TAG, "  Width: %d, Height: %d", width_, height_);
    ESP_LOGI(TAG, "  Offset: (%d, %d)", offset_x_, offset_y_);
    ESP_LOGI(TAG, "  Mirror: X=%d, Y=%d, Swap XY=%d", mirror_x_, mirror_y_, swap_xy_);

    ESP_LOGI(TAG, "Panel handles:");
    ESP_LOGI(TAG, "  Panel 1: IO=%p, Panel=%p", panel_io1_, panel1_);
    ESP_LOGI(TAG, "  Panel 2: IO=%p, Panel=%p", panel_io2_, panel2_);

    ESP_LOGI(TAG, "GIF states:");
    ESP_LOGI(TAG, "  Screen 1: decoder=%p, playing=%d, frame=%lu/%lu",
             gif_state1_.gif_decoder, gif_state1_.is_playing,
             gif_state1_.current_frame, gif_state1_.total_frames);
    ESP_LOGI(TAG, "    Buffers: frame=%p (PSRAM), line=%p (Internal, %lu lines)",
             gif_state1_.frame_buffer, gif_state1_.line_buffer, GIF_LINES_PER_BATCH);
    ESP_LOGI(TAG, "  Screen 2: decoder=%p, playing=%d, frame=%lu/%lu",
             gif_state2_.gif_decoder, gif_state2_.is_playing,
             gif_state2_.current_frame, gif_state2_.total_frames);
    ESP_LOGI(TAG, "    Buffers: frame=%p (PSRAM), line=%p (Internal, %lu lines)",
             gif_state2_.frame_buffer, gif_state2_.line_buffer, GIF_LINES_PER_BATCH);

    ESP_LOGI(TAG, "Task handles:");
    ESP_LOGI(TAG, "  Render task: %p", render_task_handle_);
    ESP_LOGI(TAG, "  Render queue: %p", render_queue_);
    ESP_LOGI(TAG, "  Mutex: %p", mutex_);

    ESP_LOGI(TAG, "=== Display Configuration Debug End ===");
}
